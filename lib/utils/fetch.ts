export const fetcher = (url: string) => fetch(url, {
  mode: 'cors',
}).then((res) => res.json())

// 通用的 fetch 封装函数
export const fetchWithOptions = async (url: string, options?: RequestInit) => {
  const response = await fetch(url, {
    mode: 'cors',
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // return response.json();
  const response_content_type = response.headers.get('Content-Type');
  switch (response_content_type) {
    case 'application/json':
      return await response.json();
    case 'text/plain':
      return await response.text();
    case 'application/octet-stream':
      return await response.blob();
    default:
      return await response.text();
  }
};

export const fileFetcher = async (url: string, { arg }: { arg: File[] }) => {
  const formData = new FormData()
  arg.forEach((f) => {
    formData.append('files', f)
  })

  const res = await fetch(url, {
    method: 'POST',
    body: formData,
  })
  return await res.blob()
}