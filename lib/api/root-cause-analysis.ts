import { fetcher, fetchWithOptions } from '../utils/fetch';

// 根因分析请求参数接口
export interface RootCauseAnalysisRequest {
    message_id: string;
    parent_message_id?: string;
    at_list?: string[];
}

// 根因分析响应接口
export interface RootCauseAnalysisResponse {
    success: boolean;
    data?: any;
    message?: string;
    error?: string;
}

/**
 * 根因分析API
 */
export const rootCauseAnalysisApi = {
    /**
     * 调用根因分析接口
     * @param messageId 消息ID
     * @param parentMessageId 父消息ID
     * @param atList @用户列表
     * @returns 分析结果
     */
    fireRootCauseAnalysis: async (
        messageId?: string,
        parentMessageId?: string,
        atList?: string[]
    ): Promise<RootCauseAnalysisResponse> => {
        try {
            if (!messageId) {
                throw new Error('消息ID不能为空');
            }

            // 构建查询字符串
            const queryParams = new URLSearchParams();
            queryParams.append('message_id', messageId);

            if (parentMessageId) {
                queryParams.append('parent_message_id', parentMessageId);
            }

            if (atList && atList.length > 0) {
                // 对于数组参数，可以使用JSON字符串或者多个同名参数
                queryParams.append('at_list', JSON.stringify(atList));
            }

            const url = `/api/rootcause?${queryParams.toString()}`;

            // 调用实际的API接口
            const response = await fetchWithOptions(url, {
                method: 'GET',
            });

            return {
                success: true,
                data: response,
                message: '根因分析请求成功'
            };

        } catch (error) {
            console.error('根因分析API调用失败:', error);

            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误',
                message: '根因分析请求失败'
            };
        }
    }
};
