'use client';

import useS<PERSON> from 'swr'
import { <PERSON>lex, <PERSON>ge, <PERSON><PERSON>, Spinner } from '@radix-ui/themes';

import { fetcher } from '@/lib/utils/fetch'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://eagle.lilithgame.com'

interface Chat {
  topic: string
  conversation_id: string
  universal_id: string
  user: string
  create_time: string
}

export default function Page() {
  const { data, error } = useSWR(
    `${API_BASE_URL}/api/admin/v2/chats`,
    fetcher,
    {
      shouldRetryOnError: false,
    }
  )
  if (error) return <p>{ error.message }</p>
  if (!data) return (
    <Flex justify='center' align='center' gap='2'>
      <Spinner />
      <p>Loading...</p>
    </Flex>
  )

  return (
    <ul role="list" className='divide-y divide-gray-100 max-w-2xl mx-auto w-full px-4 sm:px-6 lg:px-8'>
      {data.data.map((chat: Chat) => (
        <li key={chat.conversation_id} className='py-4 flex'>
          {/* <div className='min-w-0 flext-auto'>
            <p className='text-sm/6 font-semibold text-gray-900 dark:text-white'>{chat.topic} | {chat.user}</p>
            <p className='mt-1 text-xs/5 text-gray-500'>
              {chat.universal_id}
              <Badge color='gray' radius='full' variant='soft'>{chat.create_time}</Badge>
            </p>
          </div> */}

          <Flex direction='column' gap='2'>
            <Heading size='3' as='h2' >{chat.topic}</Heading>
            <Flex gap="2">
              <Badge color='blue' radius='full' variant='soft'>{chat.user}</Badge>
              <Badge color='gray' radius='full' variant='soft'>{chat.create_time}</Badge>
            </Flex>
          </Flex>
        </li>
      ))}
    </ul>
  )
}
