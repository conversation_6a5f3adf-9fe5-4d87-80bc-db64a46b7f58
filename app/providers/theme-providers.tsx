'use client'

import { Theme<PERSON>rovider as NextThemeProvider } from "next-themes"
import { Theme } from '@radix-ui/themes'
import { ReactNode, ComponentType } from 'react'

export function ThemeProvider({ children }: { children: ReactNode }) {
    const ThemeComponent = Theme as ComponentType<{ children: ReactNode }>;

    return (
        <NextThemeProvider attribute="class">
            <ThemeComponent>{children}</ThemeComponent>
        </NextThemeProvider>
    )
}