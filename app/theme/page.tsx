'use client';

import { useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { useRouter } from "next/navigation"
import { Flex, Button, Select } from "@radix-ui/themes";
import { ArrowLeftIcon } from "@radix-ui/react-icons"

const ThemeSwitch = () => {
    const [mounted, setMounted] = useState(false)
    const { theme, setTheme } = useTheme()
    const router = useRouter()

    useEffect(() => {
        setMounted(true)
    }, [])

    if (!mounted) return null

    return (
        <>
            {/* <select value={theme} onChange={(e) => setTheme(e.target.value)}>
                <option value="system">System</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
            </select> */}

            <Select.Root defaultValue={theme} onValueChange={(val) => setTheme(val)}>
                <Select.Trigger />
                <Select.Content>
                    <Select.Item value="system">System</Select.Item>
                    <Select.Item value="light">Light</Select.Item>
                    <Select.Item value="dark">Dark</Select.Item>
                </Select.Content>
            </Select.Root>


            <Flex align="center" gap="3">
                <Button variant="soft" onClick={() => router.push('/')}>
                    <ArrowLeftIcon />Back home
                </Button>
            </Flex>
        </>
    )
}

export default ThemeSwitch