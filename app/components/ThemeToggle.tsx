'use client'

import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { Icon } from './FontAwesomeIcon'

export function useThemeToggle() {
    const { theme, setTheme } = useTheme()
    const [mounted, setMounted] = useState(false)

    useEffect(() => {
        setMounted(true)
    }, [])

    const toggleTheme = () => {
        setTheme(theme === 'dark' ? 'light' : 'dark')
    }

    return {
        theme: mounted ? theme : undefined,
        toggleTheme,
        mounted
    }
}

export function ThemeToggleButton() {
    const { theme, toggleTheme, mounted } = useThemeToggle()

    if (!mounted) {
        return (
            <button className="w-10 h-10 rounded-full bg-muted animate-pulse" />
        )
    }

    return (
        <button
            onClick={toggleTheme}
            className="w-10 h-10 rounded-full bg-muted hover:bg-muted/80 flex items-center justify-center text-muted-foreground hover:text-foreground transition-all duration-300 border border-border hover:border-primary/50"
            title={`切换到${theme === 'dark' ? '浅色' : '深色'}主题`}
        >
            {theme === 'dark' ? (
                <Icon icon="fas fa-sun" className="text-lg" />
            ) : (
                <Icon icon="fas fa-moon" className="text-lg" />
            )}
        </button>
    )
}
