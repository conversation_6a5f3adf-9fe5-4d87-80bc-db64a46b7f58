'use client'

import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export default function ThemeExample() {
    const { theme, setTheme } = useTheme()
    const [mounted, setMounted] = useState(false)

    // 避免水合错误
    useEffect(() => {
        setMounted(true)
    }, [])

    if (!mounted) {
        return null
    }

    return (
        <div className="p-8 space-y-6">
            <h1 className="text-3xl font-bold text-foreground">主题示例</h1>

            {/* 主题切换按钮 */}
            <div className="flex gap-4">
                <button
                    onClick={() => setTheme('light')}
                    className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:opacity-80 transition-opacity"
                >
                    浅色主题
                </button>
                <button
                    onClick={() => setTheme('dark')}
                    className="px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:opacity-80 transition-opacity"
                >
                    深色主题
                </button>
            </div>

            {/* 卡片示例 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-card text-card-foreground p-6 rounded-lg border border-border shadow-sm">
                    <h3 className="text-lg font-semibold mb-2">主卡片</h3>
                    <p className="text-muted-foreground">
                        这是一个使用 global.css 中 @layer base 变量的卡片示例。
                    </p>
                    <button className="mt-4 px-3 py-1 bg-accent text-accent-foreground rounded hover:opacity-80">
                        操作按钮
                    </button>
                </div>

                <div className="bg-popover text-popover-foreground p-6 rounded-lg border border-border shadow-sm">
                    <h3 className="text-lg font-semibold mb-2">弹出框样式</h3>
                    <p className="text-muted-foreground">
                        这个卡片使用了 popover 的颜色变量。
                    </p>
                    <button className="mt-4 px-3 py-1 bg-destructive text-destructive-foreground rounded hover:opacity-80">
                        删除按钮
                    </button>
                </div>
            </div>

            {/* 输入框示例 */}
            <div className="space-y-4">
                <h3 className="text-xl font-semibold text-foreground">表单元素</h3>
                <div className="flex flex-col gap-3">
                    <input
                        type="text"
                        placeholder="输入文本..."
                        className="px-3 py-2 bg-background border border-input rounded-md 
                     text-foreground placeholder:text-muted-foreground 
                     focus:outline-none focus:ring-2 focus:ring-ring"
                    />
                    <textarea
                        placeholder="多行文本..."
                        rows={3}
                        className="px-3 py-2 bg-background border border-input rounded-md 
                     text-foreground placeholder:text-muted-foreground 
                     focus:outline-none focus:ring-2 focus:ring-ring resize-none"
                    />
                </div>
            </div>

            {/* 图表颜色示例 */}
            <div className="space-y-4">
                <h3 className="text-xl font-semibold text-foreground">图表颜色</h3>
                <div className="flex gap-2">
                    {[1, 2, 3, 4, 5].map((num) => (
                        <div
                            key={num}
                            className={`w-12 h-12 rounded`}
                            style={{ backgroundColor: `hsl(var(--chart-${num}))` }}
                            title={`Chart Color ${num}`}
                        />
                    ))}
                </div>
            </div>

            {/* CSS 变量直接使用示例 */}
            <div className="space-y-4">
                <h3 className="text-xl font-semibold text-foreground">CSS 变量直接使用</h3>
                <div
                    className="p-4 rounded-lg"
                    style={{
                        backgroundColor: 'hsl(var(--muted))',
                        color: 'hsl(var(--muted-foreground))',
                        border: '1px solid hsl(var(--border))'
                    }}
                >
                    这个元素直接使用了 CSS 变量的 style 属性。
                </div>
            </div>

            {/* 当前主题信息 */}
            <div className="mt-8 p-4 bg-muted text-muted-foreground rounded-lg">
                <p>当前主题: <strong>{theme}</strong></p>
                <p className="text-sm mt-1">
                    主题会自动应用对应的 CSS 变量值，实现无缝的明暗主题切换。
                </p>
            </div>
        </div>
    )
}
