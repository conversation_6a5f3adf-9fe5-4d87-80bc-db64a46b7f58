import { Icon } from './FontAwesomeIcon'

interface FeatureCardProps {
    icon: string
    title: string
    description: string
}

export default function FeatureCard({ icon, title, description }: FeatureCardProps) {
    return (
        <div className="bg-gradient-to-tr from-card via-primary/5 to-secondary/5 hover:via-primary/10 hover:to-secondary/10 backdrop-blur-sm rounded-xl p-8 transition-all duration-300 relative overflow-hidden border border-border/30 hover:border-primary/40 animate-fade-in hover:translate-y-[-10px] hover:shadow-[0_15px_40px_hsl(var(--primary)/15%)] group">
            {/* 装饰性渐变背景 */}
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/3 via-transparent to-secondary/3 group-hover:from-primary/8 group-hover:to-secondary/8 transition-all duration-300" />

            {/* 内容区域 */}
            <div className="relative z-10">
                <Icon
                    icon={icon as any}
                    className="text-4xl text-primary mb-6 block group-hover:text-primary/80 transition-colors duration-300 drop-shadow-sm"
                />
                <h3 className="text-xl mb-4 text-foreground font-semibold">{title}</h3>
                <p className="text-muted-foreground leading-relaxed">{description}</p>
            </div>
        </div>
    )
}
