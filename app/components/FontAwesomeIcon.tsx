'use client'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faEye, 
  faPlay, 
  faBolt, 
  faBrain, 
  faProjectDiagram, 
  faFeather,
  faSun,
  faMoon
} from '@fortawesome/free-solid-svg-icons'

// 图标映射
const iconMap = {
  'fas fa-eye': faEye,
  'fas fa-play': faPlay,
  'fas fa-bolt': faBolt,
  'fas fa-brain': faBrain,
  'fas fa-project-diagram': faProjectDiagram,
  'fas fa-feather': faFeather,
  'fas fa-sun': faSun,
  'fas fa-moon': faMoon,
}

interface IconProps {
  icon: keyof typeof iconMap
  className?: string
  size?: 'xs' | 'sm' | 'lg' | '1x' | '2x' | '3x' | '4x' | '5x'
}

export function Icon({ icon, className = '', size = '1x' }: IconProps) {
  const iconDefinition = iconMap[icon]
  
  if (!iconDefinition) {
    console.warn(`Icon "${icon}" not found in iconMap`)
    return null
  }

  return (
    <FontAwesomeIcon 
      icon={iconDefinition} 
      className={className}
      size={size}
    />
  )
}

// 为了向后兼容，也导出一个接受字符串类名的组件
interface LegacyIconProps {
  iconClass: string
  className?: string
}

export function LegacyIcon({ iconClass, className = '' }: LegacyIconProps) {
  const iconKey = iconClass as keyof typeof iconMap
  return <Icon icon={iconKey} className={className} />
}
