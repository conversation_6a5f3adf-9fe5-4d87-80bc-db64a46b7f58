'use client'

import { useEffect } from 'react'
import ParticlesBackground from './components/ParticlesBackground'
import { ThemeToggleButton } from './components/ThemeToggle'
import FeatureCard from './components/FeatureCard'
import { Icon } from './components/FontAwesomeIcon'

// 导航栏组件
function Navbar() {
  useEffect(() => {
    const eyeIcon = document.querySelector('.eye-icon')
    const navbar = document.querySelector('.navbar')

    if (!eyeIcon || !navbar) return

    // 眨眼动画逻辑
    const scheduleRandomBlink = () => {
      // 随机间隔：2-8秒之间
      const randomDelay = Math.random() * 6000 + 2000

      setTimeout(() => {
        eyeIcon.classList.add('animate-blink')

        // 动画结束后移除类，准备下次眨眼
        setTimeout(() => {
          eyeIcon.classList.remove('animate-blink')
          scheduleRandomBlink() // 递归调用，安排下次眨眼
        }, 600) // 0.6秒动画时长
      }, randomDelay)
    }    // 滚动检测逻辑
    const handleScroll = () => {
      const scrollY = window.scrollY
      const navbar = document.querySelector('.navbar') as HTMLElement

      if (navbar) {
        if (scrollY > 50) {
          // 滚动时显示更清晰的灰色边框和模糊阴影，同时降低高度
          navbar.classList.remove('border-transparent', 'shadow-none', 'py-5')
          navbar.classList.add('border-gray-200', 'shadow-lg', 'dark:border-gray-700', 'py-3')
        } else {
          // 在顶部时隐藏边框和阴影，恢复原始高度
          navbar.classList.remove('border-gray-200', 'shadow-lg', 'dark:border-gray-700', 'py-3')
          navbar.classList.add('border-transparent', 'shadow-none', 'py-5')
        }
      }
    }

    // 初始延迟1-3秒后开始第一次眨眼
    setTimeout(() => {
      scheduleRandomBlink()
    }, Math.random() * 2000 + 1000)

    // 添加滚动监听器
    window.addEventListener('scroll', handleScroll)

    // 初始设置
    handleScroll()

    // 清理函数（组件卸载时）
    return () => {
      eyeIcon.classList.remove('animate-blink')
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <nav className="navbar fixed top-0 left-0 w-full z-50 flex justify-between items-center px-[5%] py-5 bg-card/85 backdrop-blur-[10px] border-b border-transparent shadow-none transition-all duration-300">
      <a href="#" className="flex items-center text-3xl font-bold text-foreground no-underline">
        <Icon icon="fas fa-eye" className="text-primary mr-3 text-4xl eye-icon" />
        <span className="font-orbitron bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
          ARGUS
        </span>
      </a>
      <ul className="hidden md:flex list-none gap-9">
        <li><a href="#" className="text-muted-foreground no-underline font-medium text-lg hover:text-primary transition-all duration-300 relative py-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">产品</a></li>
        <li><a href="#" className="text-muted-foreground no-underline font-medium text-lg hover:text-primary transition-all duration-300 relative py-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">解决方案</a></li>
        <li><a href="#" className="text-muted-foreground no-underline font-medium text-lg hover:text-primary transition-all duration-300 relative py-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">客户案例</a></li>
        <li><a href="#" className="text-muted-foreground no-underline font-medium text-lg hover:text-primary transition-all duration-300 relative py-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">文档</a></li>
        <li><a href="/theme-example" className="text-muted-foreground no-underline font-medium text-lg hover:text-primary transition-all duration-300 relative py-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full">主题示例</a></li>
      </ul>
      <div className="flex items-center gap-4">
        <ThemeToggleButton />
      </div>
    </nav>
  )
}

// 主区域组件
function Hero() {
  return (
    <section className="h-screen flex items-center px-[5%] relative overflow-hidden">
      {/* 粒子背景容器 - 完全隔离 */}
      <div
        className="absolute inset-0 z-0"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflow: 'hidden',
          contain: 'layout style paint size',
          isolation: 'isolate'
        }}
      >
        <ParticlesBackground />
      </div>

      <div className="max-w-[650px] z-10 animate-fade-in relative">
        <h1 className="text-6xl mb-5 leading-tight text-foreground">
          智能根因分析，<br />
          <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            洞悉问题本质
          </span>
        </h1>
        <p className="text-xl text-muted-foreground mb-10 max-w-[600px]">
          Argus 利用人工智能技术，帮助您快速定位复杂系统中的根本原因，减少故障排查时间，提高系统稳定性。
        </p>
        <div className="flex gap-5">
          <button
            disabled
            className="bg-gradient-to-r from-muted to-muted/80 text-muted-foreground border-none px-9 py-3 rounded font-semibold text-lg cursor-not-allowed transition-all duration-300 flex items-center gap-2 opacity-60 relative group"
            title="演示功能即将开放"
          >
            查看演示
            <Icon icon="fas fa-play" className="text-sm" />
            {/* 即将开放提示 */}
            <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full text-[10px] font-medium">
              即将开放
            </span>
          </button>
        </div>
      </div>
    </section>
  )
}

// 功能特性组件
function Features() {
  const features = [
    {
      icon: "fas fa-bolt",
      title: "实时数据采集",
      description: "无缝集成各类监控数据，实时采集系统指标和日志，为分析提供全面数据支持。"
    },
    {
      icon: "fas fa-brain",
      title: "智能根因定位",
      description: "基于机器学习算法，自动分析海量数据，精准定位问题根本原因。"
    },
    {
      icon: "fas fa-project-diagram",
      title: "可视化分析",
      description: "通过交互式图表展示分析结果，清晰呈现问题链路，辅助决策。"
    },
    {
      icon: "fas fa-feather",
      title: "飞书集成",
      description: "与飞书无缝对接，支持一键分享分析结果，创建任务并协同处理故障。"
    }
  ]

  return (
    <section className="py-24 px-[5%] bg-muted/20 relative overflow-hidden">
      <div className="text-center mb-20 animate-fade-in">
        <h2 className="text-4xl mb-4 text-foreground">核心功能</h2>
        <p className="text-muted-foreground max-w-[700px] mx-auto text-lg">
          Argus 提供全方位的根因分析解决方案，助您快速定位问题根源
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 max-w-[1200px] mx-auto">
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            icon={feature.icon}
            title={feature.title}
            description={feature.description}
          />
        ))}
      </div>
    </section>
  )
}

// 可视化演示组件
function Visualization() {
  useEffect(() => {
    // 添加节点交互效果
    const nodes = document.querySelectorAll('.node')
    nodes.forEach(node => {
      node.addEventListener('click', function () {
        nodes.forEach(n => (n as HTMLElement).style.zIndex = "2")
          ; (this as HTMLElement).style.zIndex = "3"
        const currentTransform = (this as HTMLElement).style.transform
          ; (this as HTMLElement).style.transform = currentTransform.replace('scale(1)', 'scale(1.2)') || 'scale(1.2)'

          // 添加动画效果
          ; (this as HTMLElement).style.animation = "pulse 0.8s"
        setTimeout(() => {
          ; (this as HTMLElement).style.animation = "pulse 2s infinite"
        }, 800)
      })
    })
  }, [])

  return (
    <section className="py-36 px-[5%] relative overflow-hidden">
      <div className="text-center mb-20 animate-fade-in">
        <h2 className="text-4xl mb-4 text-foreground">可视化根因分析</h2>
        <p className="text-muted-foreground max-w-[700px] mx-auto text-lg">
          直观展示问题发生路径，快速识别关键节点
        </p>
      </div>
      <div className="flex flex-col lg:flex-row items-center gap-12 max-w-[1400px] mx-auto">
        <div className="flex-1 animate-fade-in">
          <h3 className="text-2xl mb-4 text-foreground">交互式问题溯源</h3>
          <p className="text-muted-foreground mb-4">
            Argus 的可视化分析工具让复杂的问题链路一目了然。通过动态节点图，您可以直观地看到问题是如何从源头传播到最终表现的。
          </p>
          <p className="text-muted-foreground mb-4">
            点击任意节点查看详细信息，系统会自动高亮相关路径，帮助您快速定位关键问题点。支持时间回溯功能，重现问题发生时的系统状态。
          </p>
          <button className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-none px-9 py-3 rounded font-semibold text-lg cursor-pointer transition-all duration-300 hover:translate-y-[-5px] hover:shadow-[0_10px_20px_hsl(var(--primary)/30%)] mt-5">
            了解更多
          </button>
        </div>
        <div className="flex-1 bg-card/60 rounded-lg p-8 border border-border/20 min-h-[400px] relative overflow-hidden animate-fade-in backdrop-blur-sm">
          <div className="relative h-[350px] w-full">
            {/* 连接线 */}
            <div className="absolute w-[150px] top-[85px] left-1/2 transform -translate-x-1/2 rotate-[-30deg] h-0.5 bg-primary/30"></div>
            <div className="absolute w-[150px] top-[85px] left-1/2 transform -translate-x-1/2 rotate-[30deg] h-0.5 bg-primary/30"></div>
            <div className="absolute w-[150px] top-[215px] left-1/4 transform rotate-90 h-0.5 bg-primary/30"></div>
            <div className="absolute w-[150px] top-[215px] left-3/4 transform rotate-90 h-0.5 bg-primary/30"></div>

            {/* 节点 */}
            <div className="node absolute top-[50px] left-1/2 transform -translate-x-1/2 w-[70px] h-[70px] rounded-full flex items-center justify-center text-primary-foreground font-bold z-10 transition-all duration-500 cursor-pointer bg-gradient-to-br from-primary to-primary/80 shadow-[0_5px_15px_hsl(var(--primary)/30%)] animate-pulse hover:scale-110 hover:shadow-[0_0_20px_hsl(var(--primary)/50%)]">
              问题现象
            </div>
            <div className="node absolute top-[180px] left-1/4 w-[70px] h-[70px] rounded-full flex items-center justify-center text-primary-foreground font-bold z-10 transition-all duration-500 cursor-pointer bg-gradient-to-br from-chart-1 to-chart-2 shadow-[0_5px_15px_hsl(var(--chart-1)/30%)] animate-pulse hover:scale-110 hover:shadow-[0_0_20px_hsl(var(--primary)/50%)]">
              服务A
            </div>
            <div className="node absolute top-[180px] left-3/4 w-[70px] h-[70px] rounded-full flex items-center justify-center text-primary-foreground font-bold z-10 transition-all duration-500 cursor-pointer bg-gradient-to-br from-chart-3 to-chart-4 shadow-[0_5px_15px_hsl(var(--chart-3)/30%)] animate-pulse hover:scale-110 hover:shadow-[0_0_20px_hsl(var(--primary)/50%)]">
              服务B
            </div>
            <div className="node absolute top-[300px] left-1/2 transform -translate-x-1/2 w-[70px] h-[70px] rounded-full flex items-center justify-center text-primary-foreground font-bold z-10 transition-all duration-500 cursor-pointer bg-gradient-to-br from-chart-5 to-primary shadow-[0_5px_15px_hsl(var(--chart-5)/30%)] animate-pulse hover:scale-110 hover:shadow-[0_0_20px_hsl(var(--primary)/50%)]">
              数据库
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

// 页脚组件
function Footer() {
  return (
    <footer className="bg-muted/20 py-16 px-[5%] border-t border-border/20">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 max-w-[1200px] mx-auto mb-12">
        <div>
          <h4 className="text-xl mb-5 text-primary">产品</h4>
          <ul className="list-none space-y-3">
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">功能</a></li>
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">解决方案</a></li>
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">演示</a></li>
          </ul>
        </div>
        <div>
          <h4 className="text-xl mb-5 text-primary">资源</h4>
          <ul className="list-none space-y-3">
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">文档</a></li>
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">博客</a></li>
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">案例研究</a></li>
            <li><a href="#" className="text-muted-foreground no-underline transition-colors hover:text-primary">支持</a></li>
          </ul>
        </div>
      </div>
      <div className="text-center pt-8 border-t border-border/20 text-muted-foreground text-sm flex items-center justify-center">
        <Icon icon="fas fa-eye" className="text-primary mr-2" />
        &copy; 2025 鹰眼团队保留所有权利。
      </div>
    </footer>
  )
}

export default function Page() {
  return (
    <div className="min-h-screen bg-background text-foreground font-exo leading-relaxed overflow-x-hidden relative">
      {/* 背景渐变 */}
      <div className="fixed top-0 left-0 w-full h-full bg-gradient-radial from-primary/5 via-transparent to-secondary/5 -z-20" />

      {/* 导航栏 */}
      <Navbar />

      {/* 主要内容 */}
      <main>
        <Hero />
        <Features />
        <Visualization />
      </main>

      {/* 页脚 */}
      <Footer />
    </div>
  )
}