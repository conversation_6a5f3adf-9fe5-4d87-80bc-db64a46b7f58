'use client';

import { useEffect, useState } from 'react';

// 定义飞书用户信息的接口
interface FeiShuUser {
    name?: string;
    email?: string;
    userId?: string;
    user_id?: string;
    user?: {
        name?: string;
        email?: string;
        user_id?: string;
    };
}

interface FeiShuInitializerProps {
    onUserInfoReceived: (userInfo: any) => void;
}

declare global {
    interface Window {
        h5sdk?: any;
        tt?: any;
    }
}

export default function FeiShuInitializer({ onUserInfoReceived }: FeiShuInitializerProps) {
    const [initializationStatus, setInitializationStatus] = useState<'pending' | 'success' | 'error'>('pending');
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [isInitialized, setIsInitialized] = useState<boolean>(false); // 添加初始化标记
    const [authDetails, setAuthDetails] = useState<{
        isAuthenticated: boolean;
        authMethod: 'config' | 'fallback';
        apiResponse?: any;
        failureReason?: string;
    } | null>(null);

    useEffect(() => {
        // 如果已经初始化过，直接返回
        if (isInitialized) {
            return;
        }

        const initializeFeiShu = async () => {
            setIsInitialized(true); // 标记为已开始初始化

            // 检查是否在飞书环境中
            if (!window.h5sdk) {
                setErrorMessage('请在飞书中打开此页面');
                setInitializationStatus('error');
                return;
            }

            // 设置错误处理
            window.h5sdk.error((err: any) => {
                setErrorMessage(`SDK错误: ${JSON.stringify(err)}`);
                setInitializationStatus('error');
            });

            try {
                // 获取当前页面URL（用于鉴权）
                const url = encodeURIComponent(location.href.split("#")[0]);

                // 尝试向后端请求鉴权参数
                const response = await fetch(`/api/rootcause/lark_auth_config?url=${url}`);

                if (response.ok) {
                    const configParams = await response.json();

                    // 保存API响应信息
                    setAuthDetails({
                        isAuthenticated: false, // 先设为false，成功后会更新
                        authMethod: 'config',
                        apiResponse: configParams
                    });

                    // 验证必要的参数是否存在
                    if (!configParams.appid || !configParams.timestamp || !configParams.noncestr || !configParams.signature) {
                        const reason = '鉴权参数不完整，缺少必要字段';
                        setAuthDetails(prev => ({ ...prev!, failureReason: reason }));
                        handleSDKReady(false, reason);
                        return;
                    }

                    // 调用config接口进行鉴权
                    window.h5sdk.config({
                        appId: configParams.appid,
                        timestamp: configParams.timestamp,
                        nonceStr: configParams.noncestr,
                        signature: configParams.signature,
                        jsApiList: [], // 根据需要添加需要的API列表
                        //鉴权成功回调
                        onSuccess: (res: any) => {
                            setAuthDetails(prev => ({ ...prev!, isAuthenticated: true }));
                            handleSDKReady(true, null, configParams); // 鉴权成功
                        },
                        //鉴权失败回调
                        onFail: (err: any) => {
                            let reason = `飞书config鉴权失败: ${JSON.stringify(err)}`;

                            // 特殊处理安全域名错误
                            if (err && (
                                JSON.stringify(err).includes('not in safe domain') ||
                                JSON.stringify(err).includes('not in safe path') ||
                                err.msg?.includes('not in safe domain') ||
                                err.message?.includes('not in safe domain')
                            )) {
                                reason = `安全域名配置错误: 当前域名 "${window.location.hostname}" 未在飞书应用后台配置的安全域名列表中`;
                            }

                            setAuthDetails(prev => ({ ...prev!, failureReason: reason }));
                            handleSDKReady(false, reason); // 鉴权失败，尝试无鉴权模式
                        }
                    });
                } else {
                    const errorText = await response.text();
                    const reason = `API请求失败: ${response.status} - ${errorText}`;
                    setAuthDetails({
                        isAuthenticated: false,
                        authMethod: 'fallback',
                        failureReason: reason
                    });
                    handleSDKReady(false, reason); // 无法获取鉴权参数，使用无鉴权模式
                }

            } catch (error) {
                const reason = `鉴权过程异常: ${error instanceof Error ? error.message : String(error)}`;
                setAuthDetails({
                    isAuthenticated: false,
                    authMethod: 'fallback',
                    failureReason: reason
                });
                handleSDKReady(false, reason); // 出错时使用无鉴权模式
            }
        };

        // 处理SDK就绪状态的函数
        const handleSDKReady = (isAuthenticated: boolean, failureReason?: string | null, apiResponse?: any) => {
            window.h5sdk.ready(() => {
                // 使用 tt.getUserInfo 获取用户信息
                if (window.tt && typeof window.tt.getUserInfo === 'function') {
                    window.tt.getUserInfo({
                        withCredentials: true,
                        success: (res: any) => {
                            // 根据官方示例，用户信息在 res.userInfo 中
                            const userInfo = res.userInfo || res;
                            const userInfoToPass = {
                                name: userInfo.nickName || userInfo.name || '飞书用户',
                                email: userInfo.email,
                                userId: userInfo.userId || userInfo.user_id,
                                avatarUrl: userInfo.avatarUrl,
                                isAuthenticated,
                                authDetails: {
                                    isAuthenticated,
                                    authMethod: isAuthenticated ? 'config' : 'fallback',
                                    apiResponse: apiResponse,
                                    failureReason: failureReason
                                }
                            };

                            onUserInfoReceived(userInfoToPass);
                            setInitializationStatus('success');
                        },
                        fail: (err: any) => {
                            // 即使获取用户信息失败，也继续使用应用
                            onUserInfoReceived({
                                name: `飞书用户${isAuthenticated ? '' : '（未鉴权）'}`,
                                isAuthenticated,
                                authDetails: {
                                    isAuthenticated,
                                    authMethod: isAuthenticated ? 'config' : 'fallback',
                                    apiResponse: apiResponse,
                                    failureReason: failureReason || `getUserInfo失败: ${JSON.stringify(err)}`
                                }
                            });
                            setInitializationStatus('success');
                        }
                    });
                } else {
                    onUserInfoReceived({
                        name: `飞书用户${isAuthenticated ? '' : '（未鉴权）'}`,
                        isAuthenticated,
                        authDetails: {
                            isAuthenticated,
                            authMethod: isAuthenticated ? 'config' : 'fallback',
                            apiResponse: apiResponse,
                            failureReason: failureReason || 'tt.getUserInfo 方法不可用'
                        }
                    });
                    setInitializationStatus('success');
                }
            });
        };

        // 延迟一点时间确保SDK加载完成
        const timer = setTimeout(() => {
            initializeFeiShu();
        }, 100);

        return () => clearTimeout(timer);
    }, []); // 移除依赖项，只在组件挂载时初始化一次

    // if (initializationStatus === 'error') {
    //     return (
    //         <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md">
    //             <p>飞书初始化失败: {errorMessage || '未知错误'}</p>
    //             <button
    //                 className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
    //                 onClick={() => window.location.reload()}
    //             >
    //                 刷新重试
    //             </button>
    //         </div>
    //     );
    // }

    return null;
}
