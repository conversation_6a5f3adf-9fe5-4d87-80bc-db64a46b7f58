'use client';

import { useEffect, useState, useCallback } from 'react';

// 消息发送者信息
export interface MessageSender {
    name: string;
    open_id: string;
}

// 提取的消息信息
export interface ExtractedMessageInfo {
    sender: MessageSender;
    chatId: string;
    messageId: string;
    messageType: string;
    content: any;
    createTime: string;
    actionTime: string;
}

// bdp_launch_query 解析结果
export interface BdpLaunchQuery {
    raw: string;
    decoded: string;
    parsed: {
        __trigger_id__: string;
        [key: string]: any;
    };
}

// 块操作源详情结果
export interface BlockActionSourceResult {
    success: boolean;
    data?: any;
    extractedInfo?: ExtractedMessageInfo;
    error?: any;
}

// 完整的源详情数据结构
export interface RelatedMessageDetail {
    currentUrl: string;
    allUrlParams: Record<string, string>;
    bdpLaunchQuery?: BdpLaunchQuery;
    triggerId?: string;
    blockActionSourceDetail?: BlockActionSourceResult;
    message?: string; // 用于显示错误或状态消息
}

interface RelatedMessageDisplayProps {
    authStatus: 'loading' | 'success' | 'failed';
    onMessageDetailReceived?: (detail: RelatedMessageDetail | null) => void;
}

// 声明全局 tt 对象类型
declare global {
    interface Window {
        tt?: any;
        h5sdk?: any;
    }
}

export default function RelatedMessageDisplay({
    authStatus,
    onMessageDetailReceived
}: RelatedMessageDisplayProps) {
    const [messageDetail, setMessageDetail] = useState<RelatedMessageDetail | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [debugInfo, setDebugInfo] = useState<string>('');

    const fetchRelatedMessageDetail = useCallback(() => {
        setDebugInfo('开始从 URL 获取参数...');
        setLoading(true);
        setError(null);

        try {
            // 获取当前页面的完整 URL
            const currentUrl = window.location.href;
            setDebugInfo(`当前 URL: ${currentUrl}`);

            // 获取 URL 参数
            const urlParams = new URLSearchParams(window.location.search);
            const allParams: any = {};

            // 收集所有 URL 参数
            urlParams.forEach((value, key) => {
                allParams[key] = value;
            });

            setDebugInfo(`URL 参数: ${JSON.stringify(allParams)}`);

            // 特别检查 bdp_launch_query 参数
            const bdpLaunchQuery = urlParams.get('bdp_launch_query');

            if (bdpLaunchQuery) {
                setDebugInfo(`找到 bdp_launch_query: ${bdpLaunchQuery}`);

                try {
                    // 解码并解析 bdp_launch_query
                    const decoded = decodeURIComponent(bdpLaunchQuery);
                    setDebugInfo(`解码后: ${decoded}`);

                    const parsed = JSON.parse(decoded);
                    setDebugInfo(`解析成功，trigger_id: ${parsed.__trigger_id__}`);

                    // 现在尝试调用 getBlockActionSourceDetail
                    if (window.tt?.getBlockActionSourceDetail) {
                        setDebugInfo(`开始调用 getBlockActionSourceDetail，triggerCode: ${parsed.__trigger_id__}`);

                        window.tt.getBlockActionSourceDetail({
                            triggerCode: parsed.__trigger_id__,
                            success: (res: any) => {
                                setDebugInfo(`getBlockActionSourceDetail 成功`);

                                // 提取关键信息
                                let extractedInfo = null;
                                if (res && res.content && res.content.messages && res.content.messages.length > 0) {
                                    const message = res.content.messages[0]; // 取第一条消息
                                    extractedInfo = {
                                        sender: message.sender,
                                        chatId: message.openChatId,
                                        messageId: message.openMessageId,
                                        messageType: message.messageType,
                                        content: message.content,
                                        createTime: message.createTime,
                                        actionTime: res.content.actionTime
                                    };
                                }

                                const result = {
                                    currentUrl,
                                    allUrlParams: allParams,
                                    bdpLaunchQuery: {
                                        raw: bdpLaunchQuery,
                                        decoded: decoded,
                                        parsed: parsed
                                    },
                                    triggerId: parsed.__trigger_id__,
                                    blockActionSourceDetail: {
                                        success: true,
                                        data: res,
                                        extractedInfo: extractedInfo
                                    }
                                };

                                setMessageDetail(result);
                                onMessageDetailReceived?.(result);
                            },
                            fail: (res: any) => {
                                setDebugInfo(`getBlockActionSourceDetail 失败: ${JSON.stringify(res)}`);

                                const result = {
                                    currentUrl,
                                    allUrlParams: allParams,
                                    bdpLaunchQuery: {
                                        raw: bdpLaunchQuery,
                                        decoded: decoded,
                                        parsed: parsed
                                    },
                                    triggerId: parsed.__trigger_id__,
                                    blockActionSourceDetail: {
                                        success: false,
                                        error: res
                                    }
                                };

                                setMessageDetail(result);
                                onMessageDetailReceived?.(result);
                            }
                        });
                    } else {
                        setDebugInfo(`window.tt.getBlockActionSourceDetail 方法不存在`);

                        const result = {
                            currentUrl,
                            allUrlParams: allParams,
                            bdpLaunchQuery: {
                                raw: bdpLaunchQuery,
                                decoded: decoded,
                                parsed: parsed
                            },
                            triggerId: parsed.__trigger_id__,
                            blockActionSourceDetail: {
                                success: false,
                                error: 'getBlockActionSourceDetail 方法不存在'
                            }
                        };

                        setMessageDetail(result);
                        onMessageDetailReceived?.(result);
                    }

                } catch (parseErr) {
                    setError(`解析 bdp_launch_query 失败: ${parseErr}`);
                    setDebugInfo(`解析错误: ${parseErr}`);
                }

            } else {
                setDebugInfo('未找到 bdp_launch_query 参数');

                // 显示所有可用的参数
                const result = {
                    currentUrl,
                    allUrlParams: allParams,
                    message: '未找到 bdp_launch_query 参数'
                };

                setMessageDetail(result);
                onMessageDetailReceived?.(result);
            }

        } catch (err) {
            const errorMsg = `获取 URL 参数失败: ${err}`;
            setError(errorMsg);
            setDebugInfo(errorMsg);
        } finally {
            setLoading(false);
        }
    }, [onMessageDetailReceived]);

    // 当授权成功后自动获取关联消息详情（只执行一次）
    useEffect(() => {
        if (authStatus === 'success') {
            // 延迟一下确保 SDK 完全初始化，只执行一次
            const timer = setTimeout(() => {
                fetchRelatedMessageDetail();
            }, 500);

            return () => clearTimeout(timer);
        }
    }, [authStatus]);

    const handleRetry = () => {
        fetchRelatedMessageDetail();
    };

    if (authStatus !== 'success') {
        return null; // 未授权时不显示
    }

    return (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg border border-blue-200 dark:border-blue-700">
            <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-3">关联消息信息</h3>

            {loading && (
                <div className="flex items-center text-blue-600 dark:text-blue-300">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    正在获取关联消息...
                </div>
            )}

            {error && (
                <div className="mb-3 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded-md">
                    <p className="text-sm mb-2">{error}</p>
                    <button
                        onClick={handleRetry}
                        className="text-xs px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                    >
                        重试
                    </button>
                </div>
            )}

            {messageDetail && !loading && (
                <div className="space-y-3">
                    {/* 提取的关键信息 - 直接在当前框内显示 */}
                    {messageDetail.blockActionSourceDetail?.extractedInfo && (
                        <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-600">
                            <span className="font-medium text-blue-700 dark:text-blue-300 block mb-2">聊天消息详情:</span>
                            <pre className="text-xs text-blue-800 dark:text-blue-200 bg-blue-100 dark:bg-blue-800 p-2 rounded overflow-auto max-h-40">
                                {JSON.stringify(messageDetail.blockActionSourceDetail.extractedInfo, null, 2)}
                            </pre>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
