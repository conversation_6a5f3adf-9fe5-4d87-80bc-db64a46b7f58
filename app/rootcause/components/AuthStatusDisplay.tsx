'use client';

interface FeiShuUserInfo {
    name?: string;
    email?: string;
    userId?: string;
    avatarUrl?: string;
    isAuthenticated?: boolean;
    authDetails?: {
        isAuthenticated: boolean;
        authMethod: 'config' | 'fallback';
        apiResponse?: any;
        failureReason?: string;
    };
}

interface AuthStatusDisplayProps {
    authStatus: 'loading' | 'success' | 'failed';
    userInfo: FeiShuUserInfo | null;
    error: string | null;
    onRetry?: () => void;
}

export default function AuthStatusDisplay({
    authStatus,
    userInfo,
    error,
    onRetry
}: AuthStatusDisplayProps) {
    if (authStatus === 'loading') {
        return (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <div className="text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-400">正在初始化飞书认证...</p>
                </div>
            </div>
        );
    }

    if (authStatus === 'failed') {
        return (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <div className="text-center">
                    <div className="mb-4">
                        <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-800 rounded-full mb-4">
                            <svg className="w-8 h-8 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-2">认证失败</h2>
                    </div>
                    <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
                        <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                            飞书认证未能成功完成
                        </p>
                        <button
                            onClick={onRetry || (() => window.location.reload())}
                            className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                        >
                            重新加载页面
                        </button>
                    </div>
                </div>

                {error && (
                    <div className="mt-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md">
                        <p>{error}</p>
                    </div>
                )}
            </div>
        );
    }

    if (authStatus === 'success' && userInfo) {
        return (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <BasicInfoSection userInfo={userInfo} />

                {error && (
                    <div className="mt-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md">
                        <p>{error}</p>
                    </div>
                )}
            </div>
        );
    }

    return null;
}

// 用户信息展示子组件
function BasicInfoSection({ userInfo }: { userInfo: FeiShuUserInfo }) {
    return (
        <div className="flex flex-col items-center mb-4 pb-4 border-b border-gray-200">
            {/* 用户头像 */}
            <div className="relative mb-3">
                {userInfo.avatarUrl ? (
                    <img
                        src={userInfo.avatarUrl}
                        alt={userInfo.name || '用户头像'}
                        className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                        onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.nextElementSibling?.classList.remove('hidden');
                        }}
                    />
                ) : null}

                {/* 默认头像 */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-bold text-xl ${userInfo.avatarUrl ? 'hidden' : ''}`}>
                    {(userInfo.name || '用户')[0]?.toUpperCase() || 'U'}
                </div>

                {/* 认证状态标识 */}
                <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white dark:border-gray-800 ${userInfo.authDetails?.isAuthenticated ? 'bg-green-500' : 'bg-yellow-500'
                    }`}>
                    <div className="w-full h-full rounded-full flex items-center justify-center">
                        {userInfo.authDetails?.isAuthenticated ? (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                        ) : (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                        )}
                    </div>
                </div>
            </div>

            {/* 用户姓名 */}
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 text-center">
                {userInfo.name || '未知用户'}
            </h3>

            {/* 用户邮箱（如果有） */}
            {userInfo.email && (
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    {userInfo.email}
                </p>
            )}
        </div>
    );
}