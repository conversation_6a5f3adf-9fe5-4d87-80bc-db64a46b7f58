'use client';

import { useState, useCallback } from 'react';
import Script from 'next/script';

import FeiShuInitializer from './components/FeiShuInitializer';
import AuthStatusDisplay from './components/AuthStatusDisplay';
import RelatedMessageDisplay, { RelatedMessageDetail } from './components/RelatedMessageDisplay';
import { rootCauseAnalysisApi } from '@/lib/api/root-cause-analysis';

interface FeiShuUserInfo {
    name?: string;
    email?: string;
    userId?: string;
    avatarUrl?: string;
    isAuthenticated?: boolean;
    authDetails?: {
        isAuthenticated: boolean;
        authMethod: 'config' | 'fallback';
        apiResponse?: any;
        failureReason?: string;
    };
}


export default function RootCauseAnalysisPage() {
    const [userInfo, setUserInfo] = useState<FeiShuUserInfo | null>(null);
    const [authStatus, setAuthStatus] = useState<'loading' | 'success' | 'failed'>('loading');
    const [error, setError] = useState<string | null>(null);
    const [_, setRelatedMessageDetail] = useState<RelatedMessageDetail | null>(null);

    const handleUserInfoReceived = useCallback((info: FeiShuUserInfo | null) => {
        setUserInfo(info);
        if (info) {
            setAuthStatus('success');
        } else {
            setAuthStatus('failed');
        }
    }, []);

    // 实现 fireRootCauseApi 函数
    const fireRootCauseApi = useCallback(async (detail: RelatedMessageDetail | null) => {
        if (!detail) {
            console.warn('没有提供详情数据，无法调用根因分析API');
            return;
        }

        try {
            // 从提取的信息中获取参数
            const extractedInfo = detail.blockActionSourceDetail?.extractedInfo;
            const messageId = extractedInfo?.messageId || detail.triggerId;
            const parentMessageId = messageId; // 使用消息ID作为父消息ID
            // const atList: string[] = []; // 暂时为空，可以后续添加逻辑提取@列表
            // const atList: string[] = [extractedInfo?.sender.open_id || '']; // 暂时为空，可以后续添加逻辑提取@列表
            const atList: string[] = [
                // user_id
                userInfo?.userId || ''
            ]; // 暂时为空，可以后续添加逻辑提取@列表

            // 调用根因分析API
            const result = await rootCauseAnalysisApi.fireRootCauseAnalysis(
                messageId,
                parentMessageId,
                atList
            );

            if (result.success) {
                // 这里可以添加成功后的处理逻辑，比如显示结果
                // 将结果显示在页面上
                window.tt.showToast({
                    "title": result.data || '根因分析成功',
                    "duration": 3000,
                    "icon": "success",
                    "mask": false,
                });
            } else {
                setError(`根因分析失败: ${result.error}`);
            }

        } catch (error) {
            setError(`调用根因分析API失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }, []);

    const handleRelatedMessageDetailReceived = useCallback((detail: RelatedMessageDetail | null) => {
        setRelatedMessageDetail(detail);

        // 调用根因分析API
        fireRootCauseApi(detail);
    }, [fireRootCauseApi]);

    return (
        <div className="container mx-auto p-4 max-w-4xl">
            {/* 飞书SDK仅在此页面加载 */}
            <Script
                src="https://lf-scm-cn.feishucdn.com/lark/op/h5-js-sdk-1.5.42.js"
                strategy="beforeInteractive"
                onError={(_) => {
                    setError(`飞书SDK加载失败，请刷新页面重试`);
                    setAuthStatus('failed');
                }}
            />

            <h1 className="text-2xl font-bold mb-6 text-center">鹰眼根因分析</h1>

            <FeiShuInitializer onUserInfoReceived={handleUserInfoReceived} />

            {/* 认证状态显示 */}
            <AuthStatusDisplay
                authStatus={authStatus}
                userInfo={userInfo}
                error={error}
                onRetry={() => window.location.reload()}
            />

            {/* 关联消息信息显示 */}
            <RelatedMessageDisplay
                authStatus={authStatus}
                onMessageDetailReceived={handleRelatedMessageDetailReceived}
            />
        </div>
    );
}
