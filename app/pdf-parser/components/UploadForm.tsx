'use client'

import { useState } from 'react'

import useSWRMutation from 'swr/mutation'
import { But<PERSON>, Strong, Text, Link, Flex, Spinner } from '@radix-ui/themes';
import { FileTextIcon } from "@radix-ui/react-icons"

import { fileFetcher } from '@/lib/utils/fetch'
import { formatFileSize } from '@/lib/utils/size'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://eagle.lilithgame.com'

export default function UploadForm() {
  const [files, setFiles] = useState<File[]>([])
  // const [isUploading, setIsUploading] = useState(false)
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files))
    }
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    if (e.dataTransfer.files) {
      const pdfFiles = Array.from(e.dataTransfer.files).filter(file =>
        file.type === 'application/pdf'
      )
      setFiles(pdfFiles)
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }

  const { trigger, isMutating, error } = useSWRMutation(
    `${API_BASE_URL}/api/pdf/extract`,
    fileFetcher
  )

  const handleSubmit = async () => {
    if (files.length === 0) return

    try {
      const blob = await trigger(files)
      const url = URL.createObjectURL(blob)
      setDownloadUrl(url)

      // 重置文件选择
      setFiles([])
      const fileInput = document.getElementById('fileInput') as HTMLInputElement
      if (fileInput) {
        fileInput.value = ''
      }
    } catch (error) {
      console.error(error)
      alert('文件上传失败，请重试')
    }
  }

  return (
    <div className="space-y-4 h-96">
      <div
        onDrop={isMutating ? undefined : handleDrop}
        onDragOver={isMutating ? undefined : handleDragOver}
        className={
          `relative border-2 border-dashed border-gray-300 rounded-lg
          h-full w-full text-center
          ${isMutating ? 'cursor-not-allowed' : 'cursor-pointer'}
          hover:border-gray-500
          max-w-md mx-auto`
        }
      >
        <input
          type="file"
          multiple
          accept=".pdf"
          onChange={handleFileChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          id="fileInput"
          disabled={isMutating}
        />
        <label
          htmlFor="fileInput"
          className="absolute inset-0 flex items-center justify-center w-full h-full text-sm text-gray-500 cursor-pointer"
        >
          {files.length > 0 ? (
            <Text as='span' className='font-bold text-gray-900 dark:text-gray-100'>
              {files.length} 个文件已选择（{formatFileSize(files.reduce((acc, file) => acc + file.size, 0))}）
            </Text>
          ) : (
            <Text as='p'>
              <Strong>拖拽</Strong>到这里，或者<Strong className='text-blue-500 hover:text-blue-700'>点击选择文件</Strong>
            </Text>
          )}
        </label>
      </div>

      <Flex direction="column" gap="3" align="center">
        <Button
          variant="soft"
          onClick={handleSubmit}
          disabled={isMutating || files.length === 0}
          className={`min-w-32 max-w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 ${isMutating ? 'cursor-wait' : 'cursor-pointer'
            }`}
        >
          <Spinner loading={isMutating}>
            <FileTextIcon />
          </Spinner>
          {isMutating ? '处理中' : '开始解析'}
        </Button>

        {downloadUrl && files.length === 0 && (
          <Link
            href={downloadUrl}
            download={`processed_files_${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}.zip`}
            className="text-blue-500 hover:text-blue-700"
          >
            点击下载处理后的文件
          </Link>
        )}
      </Flex>
    </div>
  )
}