import type { Metadata } from 'next'

import '@/styles/global.css';
import '@/app/lib/fontawesome';
import { ThemeProvider } from '@/app/providers/theme-providers';

// 使用本地字体文件，避免 Google Fonts 网络问题

export const metadata: Metadata = {
  title: 'Argus',
  description: 'Argus is a simple, fast, powerful log analysis system.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <head>
      </head>
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}