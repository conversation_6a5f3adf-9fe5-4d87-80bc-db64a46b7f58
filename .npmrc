# 使用淘宝镜像源
registry=https://registry.npmmirror.com/

auto-install-peers = true
engine-strict=true

# 允许 tsparticles 运行构建脚本
enable-pre-post-scripts=true

# 其他镜像配置
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
electron_mirror=https://npmmirror.com/mirrors/electron/
sqlite3_binary_host_mirror=https://npmmirror.com/mirrors/
profiler_binary_host_mirror=https://npmmirror.com/mirrors/node-inspector/
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/