{"scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "packageManager": "pnpm@10.11.0", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "next": "^15.3.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "swr": "^2.3.3"}, "devDependencies": {"@types/node": "^22.15.24", "@types/react": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "15.0.3", "postcss": "^8.5.4", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "3.4.17", "typescript": "^5.8.3"}, "engines": {"node": ">=20", "pnpm": ">=10"}}