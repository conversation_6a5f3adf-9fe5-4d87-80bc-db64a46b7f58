/* https://www.radix-ui.com/themes/docs/overview/styling#tailwind-base-styles */

/* @tailwind base; */
@import "tailwindcss/base";
@import "@radix-ui/themes/styles.css";

@tailwind components;
@tailwind utilities;

/* @import "@radix-ui/themes/styles.css"; */
/* :root { */
/* default theme */
/* --background: #f9f9f9; */
/* --foreground: #333; */
/* } */

/* [data-theme="dark"] { */
/* --background: #333; */
/* --foreground: #f9f9f9; */
/* --background: black;
    --foreground: white; */
/* } */


@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 142.1 76.2% 36.3%;
        --primary-foreground: 355.7 100% 97.3%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 142.1 76.2% 36.3%;
        --radius: 0.65rem;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
    }

    .dark {
        --background: 20 14.3% 4.1%;
        --foreground: 0 0% 95%;
        --card: 24 9.8% 10%;
        --card-foreground: 0 0% 95%;
        --popover: 0 0% 9%;
        --popover-foreground: 0 0% 95%;
        --primary: 142.1 70.6% 45.3%;
        --primary-foreground: 144.9 80.4% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 15%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 12 6.5% 15.1%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 85.7% 97.3%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 142.4 71.8% 29.2%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

/* 主题过渡动画 */
* {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 自定义阴影和效果 */
.theme-shadow-primary {
    box-shadow: 0 0 15px hsl(var(--primary) / 30%);
}

.theme-shadow-primary-hover:hover {
    box-shadow: 0 0 20px hsl(var(--primary) / 50%);
}

/* 渐变背景增强 */
.theme-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 80%));
}

.theme-gradient-chart {
    background: linear-gradient(135deg, hsl(var(--chart-1)), hsl(var(--chart-2)));
}