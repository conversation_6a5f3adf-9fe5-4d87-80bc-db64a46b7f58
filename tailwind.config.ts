import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // 使用 global.css 中的 @layer base 变量
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        // 保留现有的自定义颜色
        'primary-dark': '#0a192f',
        'primary-light': '#112240',
        'accent-blue': '#00d1ff',
        'accent-purple': '#9d4edd',
        'text-light': '#e6f1ff',
        'text-gray': '#8892b0',
        // 中国墨绿色科技主题
        'ink-dark': '#0d1f1a',
        'ink-medium': '#1a2e26',
        'ink-light': '#2d4a3d',
        'jade-primary': '#3eb575',
        'jade-accent': '#5dd699',
        'jade-bright': '#7fefb8',
        'emerald-glow': '#4ade80',
        'mint-subtle': '#6ee7b7',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        'sans': ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'exo': ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        'orbitron': ['Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', 'monospace'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
      keyframes: {
        pulse: {
          '0%': {
            boxShadow: '0 0 0 0 rgba(0, 209, 255, 0.4)',
          },
          '70%': {
            boxShadow: '0 0 0 10px rgba(0, 209, 255, 0)',
          },
          '100%': {
            boxShadow: '0 0 0 0 rgba(0, 209, 255, 0)',
          },
        },
        blink: {
          '0%, 70%': {
            transform: 'scaleY(1)',
          },
          '80%, 85%': {
            transform: 'scaleY(0.3)',
          },
          '90%, 95%': {
            transform: 'scaleY(0.1)',
          },
          '100%': {
            transform: 'scaleY(1)',
          },
        },
        'fade-in': {
          'from': {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          'to': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        'float': {
          '0%, 100%': {
            transform: 'translateY(0px) translateX(0px) rotate(0deg)',
          },
          '25%': {
            transform: 'translateY(-20px) translateX(10px) rotate(90deg)',
          },
          '50%': {
            transform: 'translateY(-10px) translateX(-10px) rotate(180deg)',
          },
          '75%': {
            transform: 'translateY(-30px) translateX(5px) rotate(270deg)',
          },
        },
        'spin-slow': {
          'from': {
            transform: 'rotate(0deg)',
          },
          'to': {
            transform: 'rotate(360deg)',
          },
        },
      },
      animation: {
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fade-in': 'fade-in 0.6s ease-out forwards',
        'float': 'float 4s ease-in-out infinite',
        'spin-slow': 'spin-slow 30s linear infinite',
        'blink': 'blink 0.6s ease-in-out',
      },
    },
  },
  plugins: [],
  darkMode: 'selector',
}

export default config
