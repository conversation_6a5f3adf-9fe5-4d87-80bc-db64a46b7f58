import type { NextConfig } from 'next'

// 注意：headers 配置在静态导出 (output: 'export') 时不会生效
// 如果需要设置 CSP 等头部，需要在部署环境（如 Nginx、CDN 等）中配置
// 
// Nginx 配置示例：
// location /rootcause {
//   add_header Content-Security-Policy "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://lf-scm-cn.feishucdn.com";
// }

const nextConfig: NextConfig = {
  // 启用静态导出
  output: "export",
  reactStrictMode: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  eslint: {
    ignoreDuringBuilds: true,
  }
}

export default nextConfig